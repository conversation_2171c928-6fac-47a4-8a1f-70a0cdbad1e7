import { Post, Get, Delete } from '@/request'
import { CHAIN_URL } from '../constants'

/**
 * 产业链列表
 */
export const getChainList = () => {
  return Get(`${CHAIN_URL}/merchants/industry/treeNode/new`)
}
// 产业图谱节点
export const getNodeList = code => {
  return Get(`${CHAIN_URL}/industry/chain/code/${code}/plus/tag?area_code=440306`)
}
// 产业地图子节点
export const getChildNodeList = code => {
  return Post(`${CHAIN_URL}/industry/address/chain/count`, {
    chain_codes: [code],
    agg_type: 0,
    areas: [],
  })
}

/**
 * 热力图数据
 */
export const hotCount = params => {
  return Post(`${CHAIN_URL}/industry/address/chain/count`, params)
}

/**
 *  产业图谱里面 排行前十列表
 */
export const industryListTen = params => {
  return Post(`${CHAIN_URL}/industry/new`, params)
}

/**
 *  产业地图里面 周边城市
 */
export const getRoundArea = code => {
  return Get(`${CHAIN_URL}/industry/address/addressCode/${code}`)
}
