<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 左边产业链下拉框 -->
      <div class="dropdown-container">
        <div
          class="dropdown-trigger"
          @click="toggleChainDropdown"
          :class="{ 'dropdown-actives': data.chainDropdownVisible }"
        >
          <span :class="[data.chainDropdownVisible ? 'text-[#07a6f0]' : '']">{{
            selectedChain.name || '请选择产业链'
          }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20 text-[#A0A5BA]"
            :class="[data.chainDropdownVisible ? 'rotate-180 text-[#07a6f0]' : '']"
          />
        </div>
      </div>
      <!-- 右边地区下拉框 -->
      <div class="btn_group">
        <div class="region-tab" :class="{ active: data.regionMode === 'nearby' }" @click="switchRegionMode('nearby')">
          周边
        </div>
        <div
          class="region-tab f-all-center"
          :class="{ active: data.regionMode === 'national' }"
          @click="switchRegionMode('national')"
        >
          <span>{{ selectedRegion.name || '全国' }}</span>
          <svg-icon
            v-if="data.regionMode === 'national'"
            name="arrow_d_w"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.regionDropdownVisible ? 'rotate-180' : '']"
            @click.stop="toggleRegionDropdown"
          />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="title">
        <div class="tit">
          {{ mapData.areaCode.name
          }}{{ data.regionMode === 'nearby' && mapData.areaCode.name !== '全国' ? '附近' : '' }}企业总量
        </div>
        <div class="num">{{ mapData.heatCount }}家</div>
      </div>
      <div class="w-full h-534 bg-white">
        <ChainMap :heatMapData="mapData.heatMapData" :areaCode="mapData.areaCode" :isClick="false" :tooltip="true" />
      </div>
      <div class="title">
        <div class="tit">推荐企业名单</div>
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div v-if="entListData.list.length > 0">
        <EntCard :entList="entListData.list" type="noThreeAddCollect" />
      </div>
      <!-- 空状态 -->
      <div class="noDatas" v-else-if="!entListData.loading">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>

    <!-- 产业链下拉框 -->
    <ChainSelection
      :visible="data.chainDropdownVisible"
      :defaultCode="data.selectedChainData.length > 0 ? data.selectedChainData[0].chain_code : ''"
      :headHeight="headHeight"
      @close="handleChainDropdownClose"
      @submit="handleChainSubmit"
    />

    <!-- 地区下拉框 -->
    <RegionSelection
      :visible="data.regionDropdownVisible"
      :defaultCode="data.selectedRegionData.length > 0 ? data.selectedRegionData[0].code : 'All'"
      :headHeight="headHeight"
      @close="handleRegionDropdownClose"
      @submit="handleRegionSubmit"
    />
  </div>
</template>

<script setup>
import ChainMap from './ChainMap.vue'
import ChainSelection from './ChainSelection.vue'
import RegionSelection from './RegionSelection.vue'
import EntCard from '@/components/EntCard/index.vue'
import { hotCount, industryListTen } from '@/api/iChain/index'
import { useDropdowns } from './useDropdowns.js'

const route = useRoute()
const headRef = ref(null)

// 响应式数据
const mapData = reactive({
  heatMapData: [], // 热力图数据
  heatCount: 0, // 总量
  areaCode: { name: '全国', code: '1000000', level: 1, nearCode: [] }, // 地图区域代码
})

const entListData = reactive({
  loading: false,
  list: [], // 企业列表
  total: 0,
})

const headHeight = computed(() => {
  return headRef.value?.offsetHeight || 88
})

// 根据地区级别获取 agg_type
const getAggType = (level, areaCode) => {
  if (!level || level === '0') return 2 // 全国

  const codeStr = String(areaCode || '')
  // 直辖市特殊处理
  const municipalities = ['110000', '120000', '310000', '500000']
  if (municipalities.includes(codeStr)) {
    return 6 // 直辖市用6 取区
  }

  switch (level) {
    case '1':
      return 4 // 省级
    case '2':
      return 6 // 市级取区级
    case '3':
      return 6 // 区县级 取区级
  }
}

// 构建请求参数和地图显示参数
const buildParams = (mode, selectedRegion, chainCodes) => {
  const region = selectedRegion
  // console.log('region', region)

  // 基础请求参数
  const requestParams = {
    areas: [],
    chain_codes: chainCodes,
    agg_type: 2, // 全国省级2
    level: '', // 请求推荐企业名单要用
  }

  // 基础地图显示参数
  let mapDisplayParams = {
    code: '1000000', // 默认全国
    level: '0', // 这里
    name: '全国',
  }

  // 选择全国或没有选择地区-周边和全国都是取全国参数
  if (!region.code || region.code === 'All') {
    requestParams.areas = []
    requestParams.agg_type = 2
    requestParams.level = '0'
    mapDisplayParams = { code: '1000000', level: '0', name: '全国' }
  } else {
    // 选择了具体地区
    if (mode === 'national') {
      requestParams.areas = [region.code]
      requestParams.agg_type = getAggType(region.level, region.code)
      requestParams.level = region.level
      mapDisplayParams = {
        code: region.code,
        level: region.level,
        name: region.name,
      }
    } else {
      // 4.调接口拿到周边城市编码 然后赋值给地图 赋值给请求热力图和推荐企业得参数
      // 5.地图也需要过滤 并且重新赋值
      requestParams.agg_type = 2 //固定值2
      requestParams.areas = region.nearCode
      requestParams.level = '0'
      mapDisplayParams = {
        code: '1000000', // 这里code要取全国
        level: '0',
        name: region.name, // 这里可以写全国 也可以取region.name 后续看要不要渲染
        nearCode: region.nearCode, // 这个是周边得code 数组
        nearName: region.nearName, // 这个是周边得名字数组
      }
    }
  }

  return { requestParams, mapDisplayParams }
}

// 业务逻辑：获取地图热力数据
const getMapHot = async selectionData => {
  const mode = selectionData.mode || 'national' //确认周边或者不是周边

  const selectedRegion = selectionData.regionData?.[0] || { code: 'All', level: '0', name: '全国' }
  const chainCodes = selectionData.chainData?.map(item => item.chain_code) || []

  // console.log('地图数据更新:', selectedRegion, chainCodes)
  // 构建数据请求参数和地图显示参数
  const { requestParams, mapDisplayParams } = buildParams(mode, selectedRegion, chainCodes)
  // console.log('请求参数:', requestParams)
  // console.log('地图显示参数:', mapDisplayParams)
  // 地图显示参数
  mapData.areaCode = mapDisplayParams

  try {
    // 调用热力图接口
    const response = await hotCount({
      agg_type: requestParams.agg_type,
      areas: requestParams.areas,
      chain_codes: requestParams.chain_codes,
    })
    if (response?.datalist) {
      // 转换数据格式为地图组件需要的格式
      mapData.heatMapData = response.datalist.map(item => ({
        name: item.region_name,
        value: item.count,
        region_code: item.region_code,
        region_full_title: item.region_full_title,
      }))
      mapData.heatCount = mapData.heatMapData.reduce((sum, item) => sum + item.value, 0)
    } else {
      mapData.heatMapData = []
    }
  } catch (error) {
    mapData.heatMapData = []
  }

  // 同时获取企业列表数据
  await getEntList(requestParams)
}

// 获取企业列表数据
const getEntList = async hotCountParams => {
  // 构建请求参数 - 基于热力图的参数，但添加企业列表特有的参数
  const params = {
    areas: hotCountParams.areas,
    chain_codes: hotCountParams.chain_codes,
    insight: true,
    level: '0', // pc这里是写死
    page_index: 1,
    page_size: 10,
    sort: { name: 'REGCAP', order: 'DESC' },
  }

  try {
    entListData.loading = true
    const { items = [], count = 0 } = await industryListTen(params)
    if (items.length) {
      entListData.list = items || []
      entListData.total = count || 0
    } else {
      entListData.list = []
      entListData.total = 0
    }
  } catch (error) {
    entListData.list = []
    entListData.total = 0
  } finally {
    entListData.loading = false
  }
}

// 使用下拉框 hook
const {
  data,
  selectedChain,
  selectedRegion,
  toggleChainDropdown,
  handleChainDropdownClose,
  handleChainSubmit,
  switchRegionMode,
  toggleRegionDropdown,
  handleRegionDropdownClose,
  handleRegionSubmit,
  initializeData,
} = useDropdowns(getMapHot)

onMounted(() => {
  // 初始化下拉框数据
  initializeData(route.query)
})
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
      .svg-left {
        color: red !important;
      }
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    .region-tab {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &.active {
        color: #fff !important;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        span {
          color: #fff !important;
        }
      }
    }
  }
}
.title {
  @apply flex justify-between items-center relative pl-46 pr-24 h-92 bg-white mt-24;
  border-bottom: 1px solid #eee;
  .tit {
    @apply text-32 font-semibold text-[#404040];
  }
  .num {
    @apply text-36 font-semibold text-[#7F7F7F];
  }
  &::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 36px;
    background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
  }
}
.noDatas {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100% !important;
  overflow: hidden;
  background: white;
  img {
    margin-top: 160px;
    width: 230px;
    height: 230px;
  }

  div {
    color: #74798c;
  }
}
</style>
